"""
Function Calling Orchestrator for AWS Bedrock

This module implements native function calling capabilities
for the financial analysis system.
"""

import json
import asyncio
from typing import Dict, List, Any, Optional, Tuple

from .bedrock_client import BedrockClient
from .data_sources import FinancialDataProvider
from utils.logging_utils import get_logger


class FunctionCallOrchestrator:
    """Orchestrates function calling with AWS Bedrock"""
    
    def __init__(self, bedrock_client: BedrockClient, data_provider: FinancialDataProvider):
        self.bedrock_client = bedrock_client
        self.data_provider = data_provider
        self.logger = get_logger()
        
        # Define available functions for Bedrock
        self.function_tools = [
            {
                "toolSpec": {
                    "name": "get_stock_data",
                    "description": "Get real-time stock price and basic information",
                    "inputSchema": {
                        "json": {
                            "type": "object",
                            "properties": {
                                "ticker": {
                                    "type": "string",
                                    "description": "Stock ticker symbol (e.g., AAPL, MSFT)"
                                }
                            },
                            "required": ["ticker"]
                        }
                    }
                }
            },
            {
                "toolSpec": {
                    "name": "get_financial_metrics",
                    "description": "Get comprehensive financial metrics and ratios",
                    "inputSchema": {
                        "json": {
                            "type": "object",
                            "properties": {
                                "ticker": {
                                    "type": "string",
                                    "description": "Stock ticker symbol"
                                }
                            },
                            "required": ["ticker"]
                        }
                    }
                }
            },
            {
                "toolSpec": {
                    "name": "get_technical_indicators",
                    "description": "Calculate technical analysis indicators",
                    "inputSchema": {
                        "json": {
                            "type": "object",
                            "properties": {
                                "ticker": {
                                    "type": "string",
                                    "description": "Stock ticker symbol"
                                },
                                "period": {
                                    "type": "string",
                                    "description": "Time period for analysis (1y, 6mo, 3mo)",
                                    "default": "1y"
                                }
                            },
                            "required": ["ticker"]
                        }
                    }
                }
            },
            {
                "toolSpec": {
                    "name": "get_news",
                    "description": "Get recent news articles for a stock",
                    "inputSchema": {
                        "json": {
                            "type": "object",
                            "properties": {
                                "ticker": {
                                    "type": "string",
                                    "description": "Stock ticker symbol"
                                },
                                "days_back": {
                                    "type": "integer",
                                    "description": "Number of days to look back for news",
                                    "default": 7
                                }
                            },
                            "required": ["ticker"]
                        }
                    }
                }
            },
            {
                "toolSpec": {
                    "name": "validate_ticker",
                    "description": "Validate if a ticker symbol exists and is tradeable",
                    "inputSchema": {
                        "json": {
                            "type": "object",
                            "properties": {
                                "ticker": {
                                    "type": "string",
                                    "description": "Stock ticker symbol to validate"
                                }
                            },
                            "required": ["ticker"]
                        }
                    }
                }
            }
        ]
    
    async def execute_with_functions(
        self,
        messages: List[Dict[str, Any]],
        system_message: Optional[str] = None,
        max_iterations: int = 3
    ) -> Tuple[str, List[Dict[str, Any]], Dict[str, Any]]:
        """
        Execute conversation with function calling support
        
        Args:
            messages: Conversation messages
            system_message: Optional system message
            max_iterations: Maximum function calling iterations
            
        Returns:
            Tuple of (final_response, function_calls_made, cost_info)
        """
        total_cost = 0.0
        all_function_calls = []
        current_messages = messages.copy()
        
        for iteration in range(max_iterations):
            self.logger.logger.info(f"Function calling iteration {iteration + 1}")
            
            # Call Bedrock with function tools
            response_text, function_calls, cost_info = await self.bedrock_client.converse_with_functions(
                messages=current_messages,
                tools=self.function_tools,
                system_message=system_message
            )
            
            total_cost += cost_info['total_cost']
            
            # If no function calls, we're done
            if not function_calls:
                final_cost_info = cost_info.copy()
                final_cost_info['total_cost'] = total_cost
                final_cost_info['function_calls'] = len(all_function_calls)
                return response_text, all_function_calls, final_cost_info
            
            # Execute function calls
            function_results = await self._execute_functions(function_calls)
            all_function_calls.extend(function_calls)
            
            # Add assistant message with function calls
            assistant_message = {
                "role": "assistant",
                "content": []
            }
            
            if response_text.strip():
                assistant_message["content"].append({"text": response_text})
            
            for func_call in function_calls:
                assistant_message["content"].append({
                    "toolUse": {
                        "toolUseId": func_call["toolUseId"],
                        "name": func_call["name"],
                        "input": func_call["input"]
                    }
                })
            
            current_messages.append(assistant_message)
            
            # Add function results as user messages
            for result in function_results:
                result_message = {
                    "role": "user",
                    "content": [{
                        "toolResult": {
                            "toolUseId": result["toolUseId"],
                            "content": result["content"]
                        }
                    }]
                }
                current_messages.append(result_message)
        
        # Final call without functions to get summary
        final_response, final_cost_info = await self.bedrock_client.converse_async(
            messages=current_messages,
            system_message=system_message
        )
        
        total_cost += final_cost_info['total_cost']
        
        # Extract final response text
        final_text = ""
        output_message = final_response.get('output', {}).get('message', {})
        content = output_message.get('content', [])
        
        for content_block in content:
            if 'text' in content_block:
                final_text += content_block['text']
        
        final_cost_info['total_cost'] = total_cost
        final_cost_info['function_calls'] = len(all_function_calls)
        
        return final_text, all_function_calls, final_cost_info
    
    async def _execute_functions(self, function_calls: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Execute function calls and return results"""
        results = []
        
        async with self.data_provider:
            for func_call in function_calls:
                function_name = func_call['name']
                parameters = func_call['input']
                tool_use_id = func_call['toolUseId']
                
                self.logger.logger.info(f"Executing function: {function_name} with params: {parameters}")
                
                try:
                    # Execute the appropriate function
                    if function_name == "get_stock_data":
                        result = await self._get_stock_data(parameters)
                    elif function_name == "get_financial_metrics":
                        result = await self._get_financial_metrics(parameters)
                    elif function_name == "get_technical_indicators":
                        result = await self._get_technical_indicators(parameters)
                    elif function_name == "get_news":
                        result = await self._get_news(parameters)
                    elif function_name == "validate_ticker":
                        result = await self._validate_ticker(parameters)
                    else:
                        result = f"Unknown function: {function_name}"
                    
                    results.append({
                        "toolUseId": tool_use_id,
                        "content": [{"text": result}]
                    })
                    
                except Exception as e:
                    error_message = f"Error executing {function_name}: {str(e)}"
                    self.logger.logger.error(error_message)
                    
                    results.append({
                        "toolUseId": tool_use_id,
                        "content": [{"text": error_message}]
                    })
        
        return results
    
    async def _get_stock_data(self, parameters: Dict[str, Any]) -> str:
        """Get stock data function implementation"""
        ticker = parameters['ticker']
        stock_data = await self.data_provider.get_stock_data(ticker)
        
        if not stock_data:
            return f"No stock data found for {ticker}"
        
        return json.dumps({
            "ticker": stock_data.ticker,
            "current_price": stock_data.current_price,
            "change": stock_data.change,
            "change_percent": stock_data.change_percent,
            "volume": stock_data.volume,
            "market_cap": stock_data.market_cap,
            "pe_ratio": stock_data.pe_ratio,
            "52_week_high": stock_data.fifty_two_week_high,
            "52_week_low": stock_data.fifty_two_week_low,
            "timestamp": stock_data.timestamp
        }, indent=2)
    
    async def _get_financial_metrics(self, parameters: Dict[str, Any]) -> str:
        """Get financial metrics function implementation"""
        ticker = parameters['ticker']
        metrics = await self.data_provider.get_financial_metrics(ticker)
        
        if not metrics:
            return f"No financial metrics found for {ticker}"
        
        return json.dumps({
            "ticker": metrics.ticker,
            "revenue_ttm": metrics.revenue_ttm,
            "revenue_growth": metrics.revenue_growth,
            "net_income": metrics.net_income,
            "gross_margin": metrics.gross_margin,
            "operating_margin": metrics.operating_margin,
            "profit_margin": metrics.profit_margin,
            "roe": metrics.roe,
            "roa": metrics.roa,
            "debt_to_equity": metrics.debt_to_equity,
            "current_ratio": metrics.current_ratio,
            "free_cash_flow": metrics.free_cash_flow,
            "earnings_per_share": metrics.earnings_per_share,
            "timestamp": metrics.timestamp
        }, indent=2)
    
    async def _get_technical_indicators(self, parameters: Dict[str, Any]) -> str:
        """Get technical indicators function implementation"""
        ticker = parameters['ticker']
        period = parameters.get('period', '1y')
        
        indicators = await self.data_provider.get_technical_indicators(ticker, period)
        
        if not indicators:
            return f"No technical indicators found for {ticker}"
        
        return json.dumps({
            "ticker": indicators.ticker,
            "rsi": indicators.rsi,
            "macd": indicators.macd,
            "moving_averages": indicators.moving_averages,
            "bollinger_bands": indicators.bollinger_bands,
            "support_levels": indicators.support_levels,
            "resistance_levels": indicators.resistance_levels,
            "trend": indicators.trend,
            "momentum": indicators.momentum,
            "timestamp": indicators.timestamp
        }, indent=2)
    
    async def _get_news(self, parameters: Dict[str, Any]) -> str:
        """Get news function implementation"""
        ticker = parameters['ticker']
        days_back = parameters.get('days_back', 7)
        
        news_items = await self.data_provider.get_news(ticker, days_back)
        
        if not news_items:
            return f"No recent news found for {ticker}"
        
        news_data = []
        for item in news_items:
            news_data.append({
                "title": item.title,
                "summary": item.summary,
                "source": item.source,
                "published_date": item.published_date,
                "url": item.url
            })
        
        return json.dumps({
            "ticker": ticker,
            "news_count": len(news_items),
            "news_items": news_data
        }, indent=2)
    
    async def _validate_ticker(self, parameters: Dict[str, Any]) -> str:
        """Validate ticker function implementation"""
        ticker = parameters['ticker']
        is_valid = await self.data_provider.validate_ticker(ticker)
        
        return json.dumps({
            "ticker": ticker,
            "is_valid": is_valid,
            "message": f"{ticker} is {'valid' if is_valid else 'invalid'}"
        })
